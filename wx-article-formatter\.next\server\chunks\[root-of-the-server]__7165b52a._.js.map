{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/wx_community/wx-article-formatter/src/app/api/extract/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport axios from 'axios';\nimport * as cheerio from 'cheerio';\n\ninterface ExtractedContent {\n  title: string;\n  subtitle?: string;\n  author: string;\n  authorUrl?: string;\n  authorPhoto?: string;\n  authorBio?: string;\n  summary: string;\n  hasSummary?: boolean; // 标识是否真正有摘要\n  content: string;\n  images: string[];\n  editorNote: string;\n  publishDate?: string;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json();\n    \n    if (!url) {\n      return NextResponse.json({ error: '请提供有效的URL' }, { status: 400 });\n    }\n\n    // 获取网页内容\n    const response = await axios.get(url, {\n      headers: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',\n        'Accept-Encoding': 'gzip, deflate, br',\n        'Connection': 'keep-alive',\n        'Upgrade-Insecure-Requests': '1'\n      },\n      timeout: 15000,\n      maxRedirects: 5,\n      responseType: 'text'\n    });\n\n    const $ = cheerio.load(response.data) as any;\n\n    // 检测是否为附件HTML格式\n    const isAttachmentHtml = detectAttachmentHtml($);\n\n    // 检测是否为银河悦读等特定格式\n    const isArticleBoxFormat = detectArticleBoxFormat($);\n\n    // 提取内容的策略选择\n    let extractedContent: ExtractedContent;\n\n    if (isArticleBoxFormat) {\n      extractedContent = await extractArticleBoxContent($);\n    } else if (isAttachmentHtml) {\n      extractedContent = {\n        title: extractTitle($),\n        subtitle: '',\n        author: extractAuthor($),\n        authorUrl: '',\n        authorPhoto: '',\n        authorBio: '',\n        summary: extractSummary($),\n        hasSummary: false, // 其他格式不显示摘要\n        content: extractAttachmentContent($),\n        images: extractImages($),\n        editorNote: extractEditorNote($),\n        publishDate: extractPublishDate($)\n      };\n    } else {\n      extractedContent = {\n        title: extractTitle($),\n        subtitle: '',\n        author: extractAuthor($),\n        authorUrl: '',\n        authorPhoto: '',\n        authorBio: '',\n        summary: extractSummary($),\n        hasSummary: false, // 其他格式不显示摘要\n        content: extractContent($),\n        images: extractImages($),\n        editorNote: extractEditorNote($),\n        publishDate: extractPublishDate($)\n      };\n    }\n\n    return NextResponse.json(extractedContent);\n  } catch (error) {\n    console.error('提取内容失败:', error);\n    return NextResponse.json(\n      { error: '提取内容失败，请检查URL是否有效' }, \n      { status: 500 }\n    );\n  }\n}\n\n// 提取标题\nfunction extractTitle($: cheerio.CheerioAPI): string {\n  // 尝试多种选择器，包括常见的CMS和附件格式\n  const titleSelectors = [\n    'h1',\n    '.title',\n    '.article-title',\n    '.post-title',\n    '.entry-title',\n    '.content-title',\n    '.main-title',\n    '.doc-title',\n    '.attachment-title',\n    '[class*=\"title\"]',\n    '[id*=\"title\"]',\n    'title',\n    // 针对附件HTML格式\n    '.WordSection1 h1',\n    '.WordSection1 p[style*=\"font-size:18\"]',\n    '.WordSection1 p[style*=\"font-size:20\"]',\n    '.WordSection1 p[style*=\"font-size:22\"]',\n    '.WordSection1 p[style*=\"font-weight:bold\"]',\n    // 微信公众号格式\n    '#activity-name',\n    '.rich_media_title',\n    // 知乎、简书等格式\n    '.Post-Title',\n    '.article h1',\n    '.note h1'\n  ];\n\n  for (const selector of titleSelectors) {\n    const title = $(selector).first().text().trim();\n    if (title && title.length > 0 && title.length < 200) {\n      return title;\n    }\n  }\n\n  // 如果没有找到标题，尝试从第一个较大的文本块提取\n  const firstLargeText = $('p, div').filter((_, el) => {\n    const text = $(el).text().trim();\n    return text.length > 10 && text.length < 100;\n  }).first().text().trim();\n\n  return firstLargeText || '未找到标题';\n}\n\n// 提取作者\nfunction extractAuthor($: cheerio.CheerioAPI): string {\n  const authorSelectors = [\n    '.author',\n    '.byline',\n    '.writer',\n    '.post-author',\n    '.article-author',\n    '.content-author',\n    '[class*=\"author\"]',\n    '[class*=\"byline\"]',\n    '[class*=\"writer\"]',\n    // 微信公众号格式\n    '.rich_media_meta_text',\n    '#profileBt',\n    // 知乎、简书等格式\n    '.UserLink',\n    '.author-name',\n    // 附件HTML格式中可能的作者信息\n    '.WordSection1 p:contains(\"作者\")',\n    '.WordSection1 p:contains(\"撰写\")',\n    'meta[name=\"author\"]'\n  ];\n\n  for (const selector of authorSelectors) {\n    let author = '';\n    if (selector.startsWith('meta')) {\n      author = $(selector).attr('content') || '';\n    } else {\n      author = $(selector).first().text().trim();\n      // 清理作者信息\n      author = author.replace(/^(作者[:：]?|撰写[:：]?|by[:：]?\\s*)/i, '').trim();\n    }\n\n    if (author && author.length > 0 && author.length < 50) {\n      return author;\n    }\n  }\n\n  return '未知作者';\n}\n\n// 提取摘要\nfunction extractSummary($: cheerio.CheerioAPI): string {\n  const summarySelectors = [\n    '.summary',\n    '.abstract',\n    '.excerpt',\n    '.description',\n    '[class*=\"summary\"]',\n    '[class*=\"abstract\"]',\n    'meta[name=\"description\"]'\n  ];\n\n  for (const selector of summarySelectors) {\n    let summary = '';\n    if (selector.startsWith('meta')) {\n      summary = $(selector).attr('content') || '';\n    } else {\n      summary = $(selector).first().text().trim();\n    }\n\n    // 清理摘要文本，去掉重复的\"摘要：\"\n    summary = summary.replace(/^摘要[：:]?\\s*/i, '');\n\n    if (summary && summary.trim().length > 5) {\n      return summary.trim();\n    }\n  }\n\n  // 如果没有找到摘要，尝试从正文前几段提取\n  const firstParagraphs = $('p').slice(0, 3).map((_, el) => $(el).text().trim()).get();\n  let summary = firstParagraphs.join(' ').substring(0, 200);\n\n  // 清理提取的摘要\n  summary = summary.replace(/^摘要[：:]?\\s*/i, '');\n\n  return summary.trim() || '';\n}\n\n// 提取正文内容\nfunction extractContent($: cheerio.CheerioAPI): string {\n  const contentSelectors = [\n    '.content',\n    '.article-content',\n    '.post-content',\n    '.entry-content',\n    '.main-content',\n    '.text-content',\n    '.rich_media_content',\n    '[class*=\"content\"]',\n    'article',\n    // 针对附件HTML格式\n    '.WordSection1',\n    '.MsoNormal',\n    '.document-content',\n    // 微信公众号格式\n    '#js_content',\n    '.rich_media_area_primary',\n    // 知乎、简书等格式\n    '.Post-RichText',\n    '.note .content',\n    'main'\n  ];\n\n  for (const selector of contentSelectors) {\n    const content = $(selector).first();\n    if (content.length > 0) {\n      // 移除不需要的元素\n      content.find('script, style, nav, header, footer, aside, .ad, .advertisement, .share, .comment').remove();\n\n      // 获取文本内容，保留段落结构\n      const paragraphs = content.find('p, div').map((_, el) => {\n        const $el = $(el);\n        const text = $el.text().trim();\n\n        // 过滤掉太短或明显是导航/广告的内容，以及版权声明\n        if (text.length < 10 ||\n            text.includes('点击') ||\n            text.includes('关注') ||\n            text.includes('分享') ||\n            text.includes('广告') ||\n            text.includes('本网站作品著作权归作者本人所有') ||\n            text.includes('未经作者本人授权，不得转载') ||\n            $el.hasClass('ad') ||\n            $el.hasClass('share')) {\n          return null;\n        }\n\n        return text;\n      }).get().filter(Boolean);\n\n      if (paragraphs.length > 0) {\n        return paragraphs.join('\\n\\n');\n      }\n\n      // 如果没有段落，直接获取文本内容\n      const textContent = content.text().trim();\n      if (textContent.length > 50) {\n        return textContent;\n      }\n    }\n  }\n\n  // 如果没有找到特定的内容区域，尝试提取所有段落\n  const allParagraphs = $('p, div').map((_, el) => {\n    const text = $(el).text().trim();\n    // 过滤掉太短的段落和明显的导航内容，以及版权声明\n    if (text.length < 20 ||\n        text.includes('首页') ||\n        text.includes('导航') ||\n        text.includes('菜单') ||\n        text.includes('版权') ||\n        text.includes('Copyright') ||\n        text.includes('本网站作品著作权归作者本人所有') ||\n        text.includes('未经作者本人授权，不得转载')) {\n      return null;\n    }\n    return text;\n  }).get().filter(Boolean);\n\n  return allParagraphs.length > 0 ? allParagraphs.join('\\n\\n') : '未找到正文内容';\n}\n\n// 提取图片\nfunction extractImages($: cheerio.CheerioAPI): string[] {\n  const images: string[] = [];\n  \n  // 查找文章内容区域的图片\n  const contentSelectors = [\n    '.content img',\n    '.article-content img',\n    '.post-content img',\n    '.entry-content img',\n    'article img'\n  ];\n\n  for (const selector of contentSelectors) {\n    $(selector).each((_, el) => {\n      const src = $(el).attr('src');\n      if (src && !images.includes(src)) {\n        // 处理相对路径\n        if (src.startsWith('//')) {\n          images.push('https:' + src);\n        } else if (src.startsWith('/')) {\n          // 需要基础URL，这里暂时跳过\n        } else if (src.startsWith('http')) {\n          images.push(src);\n        }\n      }\n    });\n  }\n\n  return images.slice(0, 10); // 限制最多10张图片\n}\n\n// 提取编者按\nfunction extractEditorNote($: cheerio.CheerioAPI): string {\n  const editorNoteSelectors = [\n    '.editor-note',\n    '.editorial',\n    '.note',\n    '[class*=\"editor\"]',\n    '[class*=\"note\"]'\n  ];\n\n  for (const selector of editorNoteSelectors) {\n    const note = $(selector).first().text().trim();\n    if (note && note.length > 0) {\n      return note;\n    }\n  }\n\n  return '';\n}\n\n// 提取发布日期\nfunction extractPublishDate($: cheerio.CheerioAPI): string {\n  const dateSelectors = [\n    '.publish-date',\n    '.date',\n    '.post-date',\n    '[class*=\"date\"]',\n    'time',\n    'meta[property=\"article:published_time\"]'\n  ];\n\n  for (const selector of dateSelectors) {\n    let date = '';\n    if (selector.startsWith('meta')) {\n      date = $(selector).attr('content') || '';\n    } else if (selector === 'time') {\n      date = $(selector).attr('datetime') || $(selector).text().trim();\n    } else {\n      date = $(selector).first().text().trim();\n    }\n    \n    if (date && date.length > 0) {\n      return date;\n    }\n  }\n\n  return '';\n}\n\n// 检测是否为附件HTML格式\nfunction detectAttachmentHtml($: cheerio.CheerioAPI): boolean {\n  // 检查常见的附件HTML特征\n  const attachmentIndicators = [\n    '.WordSection1',\n    '.MsoNormal',\n    '[class*=\"Mso\"]',\n    'style[type=\"text/css\"]',\n    'meta[name=\"Generator\"][content*=\"Microsoft\"]',\n    'meta[name=\"ProgId\"][content*=\"Word\"]'\n  ];\n\n  for (const indicator of attachmentIndicators) {\n    if ($(indicator).length > 0) {\n      return true;\n    }\n  }\n\n  // 检查HTML内容是否包含Word特有的样式\n  const htmlContent = $.html();\n  const wordPatterns = [\n    /mso-/i,\n    /WordSection/i,\n    /MsoNormal/i,\n    /Microsoft Office Word/i,\n    /<!--\\[if gte mso/i\n  ];\n\n  return wordPatterns.some(pattern => pattern.test(htmlContent));\n}\n\n// 专门处理附件HTML格式的内容提取\nfunction extractAttachmentContent($: cheerio.CheerioAPI): string {\n  const contentSections = [];\n\n  // 优先从WordSection1提取\n  const wordSection = $('.WordSection1');\n  if (wordSection.length > 0) {\n    const paragraphs = wordSection.find('p, div').map((_, el) => {\n      const $el = $(el);\n      let text = $el.text().trim();\n\n      // 跳过空段落和页眉页脚\n      if (!text ||\n          text.length < 5 ||\n          text.includes('页码') ||\n          text.includes('第') && text.includes('页') ||\n          $el.hasClass('header') ||\n          $el.hasClass('footer')) {\n        return null;\n      }\n\n      // 清理Word特有的格式字符\n      text = text.replace(/\\u00A0/g, ' '); // 替换不间断空格\n      text = text.replace(/\\s+/g, ' '); // 合并多个空格\n      text = text.trim();\n\n      return text;\n    }).get().filter(Boolean);\n\n    if (paragraphs.length > 0) {\n      contentSections.push(...paragraphs);\n    }\n  }\n\n  // 如果WordSection1没有内容，尝试其他选择器\n  if (contentSections.length === 0) {\n    const msoElements = $('.MsoNormal, [class*=\"Mso\"]');\n    msoElements.each((_, el) => {\n      const text = $(el).text().trim();\n      if (text && text.length > 10) {\n        contentSections.push(text);\n      }\n    });\n  }\n\n  // 如果还是没有内容，使用通用提取方法\n  if (contentSections.length === 0) {\n    return extractContent($);\n  }\n\n  return contentSections.join('\\n\\n');\n}\n\n// 检测是否为article-box格式（银河悦读等）\nfunction detectArticleBoxFormat($: cheerio.CheerioAPI): boolean {\n  return $('.article-box').length > 0 || $('#content').length > 0;\n}\n\n// 专门处理article-box格式的内容提取\nasync function extractArticleBoxContent($: cheerio.CheerioAPI): Promise<ExtractedContent> {\n  const result: ExtractedContent = {\n    title: '',\n    subtitle: '',\n    author: '',\n    authorUrl: '',\n    authorPhoto: '',\n    authorBio: '',\n    summary: '',\n    hasSummary: false,\n    content: '',\n    images: [],\n    editorNote: '',\n    publishDate: ''\n  };\n\n  // 从article-head提取标题和作者\n  const articleHead = $('.article-head');\n  if (articleHead.length > 0) {\n    // 提取标题 - 优化处理h1标签内的复杂结构\n    const titleElement = articleHead.find('h1').first();\n    if (titleElement.length > 0) {\n      // 克隆元素以避免修改原DOM\n      const titleClone = titleElement.clone();\n\n      // 移除图片标签，只保留文本\n      titleClone.find('img').remove();\n\n      // 提取主标题（第一行文本）\n      const titleText = titleClone.contents().filter(function() {\n        return this.nodeType === 3; // 文本节点\n      }).text().trim();\n\n      // 提取副标题（div中的内容）\n      const subtitleDiv = titleClone.find('div').first();\n      let subtitle = '';\n      if (subtitleDiv.length > 0) {\n        subtitle = subtitleDiv.text().trim();\n        // 去掉副标题前的破折号\n        subtitle = subtitle.replace(/^——/, '').trim();\n      }\n\n      // 分别设置主标题和副标题\n      if (titleText) {\n        result.title = titleText;\n        if (subtitle) {\n          result.subtitle = subtitle;\n        }\n      } else {\n        // 如果没有提取到文本节点，使用整个h1的文本但清理格式\n        const fullTitle = titleElement.text().trim().replace(/\\s+/g, ' ');\n        // 尝试分离主标题和副标题\n        if (fullTitle.includes('——')) {\n          const parts = fullTitle.split('——');\n          result.title = parts[0].trim();\n          result.subtitle = parts[1].trim();\n        } else {\n          result.title = fullTitle;\n        }\n      }\n    }\n\n    // 提取作者 - 专门处理嵌套的span和a标签结构\n    const authorSpan = articleHead.find('span').filter((_, el) => {\n      return $(el).text().includes('作者：');\n    }).first();\n\n    if (authorSpan.length > 0) {\n      // 查找span内的a标签\n      const authorLink = authorSpan.find('a').first();\n      if (authorLink.length > 0) {\n        result.author = authorLink.text().trim();\n        result.authorUrl = authorLink.attr('href') || '';\n      } else {\n        // 如果没有a标签，从span文本中提取\n        const spanText = authorSpan.text().trim();\n        result.author = spanText.replace(/^作者[:：]?\\s*/, '').trim();\n      }\n    }\n\n    // 如果上面的方法没有找到作者，使用通用方法\n    if (!result.author) {\n      articleHead.find('*').each((_, el) => {\n        const text = $(el).text().trim();\n        if (text.includes('作者') || text.includes('撰写') || text.includes('by') || text.includes('By')) {\n          // 提取作者名字，去掉\"作者：\"等前缀\n          result.author = text.replace(/^(作者[:：]?|撰写[:：]?|by[:：]?\\s*)/i, '').trim();\n          if (result.author.length > 50) result.author = ''; // 太长的可能不是作者名\n          return false; // 找到后停止\n        }\n      });\n    }\n\n    // 不从头部提取图片，因为头部的图片通常是装饰性的（如推荐图标等）\n  }\n\n  // 从article-abs提取摘要\n  const articleAbs = $('.article-abs');\n  if (articleAbs.length > 0) {\n    let summaryText = articleAbs.text().trim();\n\n    // 清理摘要文本，去掉重复的\"摘要：\"\n    summaryText = summaryText.replace(/^摘要[：:]?\\s*/i, '');\n\n    // 只有当清理后的摘要有实际内容时才使用\n    if (summaryText && summaryText.trim().length > 5) {\n      result.summary = summaryText.trim();\n      result.hasSummary = true; // 标记真正有摘要\n    }\n  }\n\n  // 从article-text或#content1提取正文\n  const articleText = $('.article-text, #content1').first();\n  if (articleText.length > 0) {\n    // 移除不需要的元素\n    const contentClone = articleText.clone();\n    contentClone.find('script, style, .ad, .advertisement, .share, .comment, .nav, .navigation').remove();\n\n    // 提取段落\n    const paragraphs = contentClone.find('p, div').map((_, el) => {\n      const text = $(el).text().trim();\n      // 过滤掉太短的段落和明显的导航/广告内容，以及版权声明\n      if (text.length < 10 ||\n          text.includes('点击') ||\n          text.includes('关注') ||\n          text.includes('分享') ||\n          text.includes('广告') ||\n          text.includes('评论') ||\n          text.includes('发表时间') ||\n          text.includes('阅读量') ||\n          text.includes('本网站作品著作权归作者本人所有') ||\n          text.includes('未经作者本人授权，不得转载')) {\n        return null;\n      }\n      return text;\n    }).get().filter(Boolean);\n\n    if (paragraphs.length > 0) {\n      result.content = paragraphs.join('\\n\\n');\n    } else {\n      // 如果没有段落，直接获取文本内容\n      result.content = contentClone.text().trim();\n    }\n\n    // 只从正文区域提取图片\n    articleText.find('img').each((_, img) => {\n      const src = $(img).attr('src');\n      if (src) {\n        // 处理相对路径和协议相对路径\n        let fullSrc = src;\n        if (src.startsWith('//')) {\n          fullSrc = 'https:' + src;\n        } else if (src.startsWith('/')) {\n          // 需要基础URL，这里暂时跳过相对路径\n          return;\n        }\n\n        if (!result.images.includes(fullSrc)) {\n          result.images.push(fullSrc);\n        }\n      }\n    });\n  }\n\n  // 提取标签（如果有的话）\n  const tagsElement = $('.article-notes-title, p.article-notes-title');\n  if (tagsElement.length > 0) {\n    const tagsText = tagsElement.text().trim();\n    if (tagsText.includes('标签')) {\n      // 可以将标签信息添加到摘要或其他字段\n      if (!result.summary) {\n        result.summary = tagsText;\n      }\n    }\n  }\n\n  // 提取编者按\n  const editorNote = $('.article-notes-body, p.article-notes-body');\n  if (editorNote.length > 0) {\n    result.editorNote = editorNote.text().trim();\n  }\n\n  // 如果没有提取到标题，尝试从其他地方获取\n  if (!result.title) {\n    result.title = extractTitle($);\n  }\n\n  // 如果没有提取到作者，尝试从其他地方获取\n  if (!result.author) {\n    result.author = extractAuthor($);\n  }\n\n  // 不自动生成摘要，只有真正存在article-abs时才有摘要\n\n  // 如果有作者链接，尝试提取作者简介\n  if (result.authorUrl) {\n    try {\n      await extractAuthorBio(result);\n    } catch (error) {\n      console.error('提取作者简介失败:', error);\n      // 不影响主要功能，继续执行\n    }\n  }\n\n  return result;\n}\n\n// 提取作者简介\nasync function extractAuthorBio(result: ExtractedContent) {\n  if (!result.authorUrl) return;\n\n  try {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 10000);\n\n    const response = await fetch(result.authorUrl, {\n      headers: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n      },\n      signal: controller.signal,\n    });\n\n    clearTimeout(timeoutId);\n\n    if (!response.ok) return;\n\n    const html = await response.text();\n    const $ = cheerio.load(html);\n\n    // 提取作者照片\n    const photoDiv = $('.zj-block-pic img').first();\n    if (photoDiv.length > 0) {\n      result.authorPhoto = photoDiv.attr('src') || '';\n    }\n\n    // 提取作者简介\n    const bioDiv = $('.zj-block-cont').first();\n    if (bioDiv.length > 0) {\n      result.authorBio = bioDiv.text().trim();\n    }\n  } catch (error) {\n    console.error('获取作者页面失败:', error);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAiBO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAY,GAAG;gBAAE,QAAQ;YAAI;QACjE;QAEA,SAAS;QACT,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;YACpC,SAAS;gBACP,cAAc;gBACd,UAAU;gBACV,mBAAmB;gBACnB,mBAAmB;gBACnB,cAAc;gBACd,6BAA6B;YAC/B;YACA,SAAS;YACT,cAAc;YACd,cAAc;QAChB;QAEA,MAAM,IAAI,yJAAA,CAAA,OAAY,CAAC,SAAS,IAAI;QAEpC,gBAAgB;QAChB,MAAM,mBAAmB,qBAAqB;QAE9C,iBAAiB;QACjB,MAAM,qBAAqB,uBAAuB;QAElD,YAAY;QACZ,IAAI;QAEJ,IAAI,oBAAoB;YACtB,mBAAmB,MAAM,yBAAyB;QACpD,OAAO,IAAI,kBAAkB;YAC3B,mBAAmB;gBACjB,OAAO,aAAa;gBACpB,UAAU;gBACV,QAAQ,cAAc;gBACtB,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,SAAS,eAAe;gBACxB,YAAY;gBACZ,SAAS,yBAAyB;gBAClC,QAAQ,cAAc;gBACtB,YAAY,kBAAkB;gBAC9B,aAAa,mBAAmB;YAClC;QACF,OAAO;YACL,mBAAmB;gBACjB,OAAO,aAAa;gBACpB,UAAU;gBACV,QAAQ,cAAc;gBACtB,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,SAAS,eAAe;gBACxB,YAAY;gBACZ,SAAS,eAAe;gBACxB,QAAQ,cAAc;gBACtB,YAAY,kBAAkB;gBAC9B,aAAa,mBAAmB;YAClC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoB,GAC7B;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,OAAO;AACP,SAAS,aAAa,CAAqB;IACzC,wBAAwB;IACxB,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA,UAAU;QACV;QACA;QACA,WAAW;QACX;QACA;QACA;KACD;IAED,KAAK,MAAM,YAAY,eAAgB;QACrC,MAAM,QAAQ,EAAE,UAAU,KAAK,GAAG,IAAI,GAAG,IAAI;QAC7C,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,KAAK;YACnD,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,EAAE,UAAU,MAAM,CAAC,CAAC,GAAG;QAC5C,MAAM,OAAO,EAAE,IAAI,IAAI,GAAG,IAAI;QAC9B,OAAO,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG;IAC3C,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI;IAEtB,OAAO,kBAAkB;AAC3B;AAEA,OAAO;AACP,SAAS,cAAc,CAAqB;IAC1C,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,UAAU;QACV;QACA;QACA,WAAW;QACX;QACA;QACA,mBAAmB;QACnB;QACA;QACA;KACD;IAED,KAAK,MAAM,YAAY,gBAAiB;QACtC,IAAI,SAAS;QACb,IAAI,SAAS,UAAU,CAAC,SAAS;YAC/B,SAAS,EAAE,UAAU,IAAI,CAAC,cAAc;QAC1C,OAAO;YACL,SAAS,EAAE,UAAU,KAAK,GAAG,IAAI,GAAG,IAAI;YACxC,SAAS;YACT,SAAS,OAAO,OAAO,CAAC,kCAAkC,IAAI,IAAI;QACpE;QAEA,IAAI,UAAU,OAAO,MAAM,GAAG,KAAK,OAAO,MAAM,GAAG,IAAI;YACrD,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,OAAO;AACP,SAAS,eAAe,CAAqB;IAC3C,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,YAAY,iBAAkB;QACvC,IAAI,UAAU;QACd,IAAI,SAAS,UAAU,CAAC,SAAS;YAC/B,UAAU,EAAE,UAAU,IAAI,CAAC,cAAc;QAC3C,OAAO;YACL,UAAU,EAAE,UAAU,KAAK,GAAG,IAAI,GAAG,IAAI;QAC3C;QAEA,oBAAoB;QACpB,UAAU,QAAQ,OAAO,CAAC,gBAAgB;QAE1C,IAAI,WAAW,QAAQ,IAAI,GAAG,MAAM,GAAG,GAAG;YACxC,OAAO,QAAQ,IAAI;QACrB;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,EAAE,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,KAAO,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG;IAClF,IAAI,UAAU,gBAAgB,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG;IAErD,UAAU;IACV,UAAU,QAAQ,OAAO,CAAC,gBAAgB;IAE1C,OAAO,QAAQ,IAAI,MAAM;AAC3B;AAEA,SAAS;AACT,SAAS,eAAe,CAAqB;IAC3C,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,aAAa;QACb;QACA;QACA;QACA,UAAU;QACV;QACA;QACA,WAAW;QACX;QACA;QACA;KACD;IAED,KAAK,MAAM,YAAY,iBAAkB;QACvC,MAAM,UAAU,EAAE,UAAU,KAAK;QACjC,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,WAAW;YACX,QAAQ,IAAI,CAAC,oFAAoF,MAAM;YAEvG,gBAAgB;YAChB,MAAM,aAAa,QAAQ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG;gBAChD,MAAM,MAAM,EAAE;gBACd,MAAM,OAAO,IAAI,IAAI,GAAG,IAAI;gBAE5B,2BAA2B;gBAC3B,IAAI,KAAK,MAAM,GAAG,MACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,sBACd,KAAK,QAAQ,CAAC,oBACd,IAAI,QAAQ,CAAC,SACb,IAAI,QAAQ,CAAC,UAAU;oBACzB,OAAO;gBACT;gBAEA,OAAO;YACT,GAAG,GAAG,GAAG,MAAM,CAAC;YAEhB,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,OAAO,WAAW,IAAI,CAAC;YACzB;YAEA,kBAAkB;YAClB,MAAM,cAAc,QAAQ,IAAI,GAAG,IAAI;YACvC,IAAI,YAAY,MAAM,GAAG,IAAI;gBAC3B,OAAO;YACT;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB,EAAE,UAAU,GAAG,CAAC,CAAC,GAAG;QACxC,MAAM,OAAO,EAAE,IAAI,IAAI,GAAG,IAAI;QAC9B,0BAA0B;QAC1B,IAAI,KAAK,MAAM,GAAG,MACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,gBACd,KAAK,QAAQ,CAAC,sBACd,KAAK,QAAQ,CAAC,kBAAkB;YAClC,OAAO;QACT;QACA,OAAO;IACT,GAAG,GAAG,GAAG,MAAM,CAAC;IAEhB,OAAO,cAAc,MAAM,GAAG,IAAI,cAAc,IAAI,CAAC,UAAU;AACjE;AAEA,OAAO;AACP,SAAS,cAAc,CAAqB;IAC1C,MAAM,SAAmB,EAAE;IAE3B,cAAc;IACd,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,YAAY,iBAAkB;QACvC,EAAE,UAAU,IAAI,CAAC,CAAC,GAAG;YACnB,MAAM,MAAM,EAAE,IAAI,IAAI,CAAC;YACvB,IAAI,OAAO,CAAC,OAAO,QAAQ,CAAC,MAAM;gBAChC,SAAS;gBACT,IAAI,IAAI,UAAU,CAAC,OAAO;oBACxB,OAAO,IAAI,CAAC,WAAW;gBACzB,OAAO,IAAI,IAAI,UAAU,CAAC,MAAM;gBAC9B,iBAAiB;gBACnB,OAAO,IAAI,IAAI,UAAU,CAAC,SAAS;oBACjC,OAAO,IAAI,CAAC;gBACd;YACF;QACF;IACF;IAEA,OAAO,OAAO,KAAK,CAAC,GAAG,KAAK,YAAY;AAC1C;AAEA,QAAQ;AACR,SAAS,kBAAkB,CAAqB;IAC9C,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,YAAY,oBAAqB;QAC1C,MAAM,OAAO,EAAE,UAAU,KAAK,GAAG,IAAI,GAAG,IAAI;QAC5C,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC3B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS;AACT,SAAS,mBAAmB,CAAqB;IAC/C,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,YAAY,cAAe;QACpC,IAAI,OAAO;QACX,IAAI,SAAS,UAAU,CAAC,SAAS;YAC/B,OAAO,EAAE,UAAU,IAAI,CAAC,cAAc;QACxC,OAAO,IAAI,aAAa,QAAQ;YAC9B,OAAO,EAAE,UAAU,IAAI,CAAC,eAAe,EAAE,UAAU,IAAI,GAAG,IAAI;QAChE,OAAO;YACL,OAAO,EAAE,UAAU,KAAK,GAAG,IAAI,GAAG,IAAI;QACxC;QAEA,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC3B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,gBAAgB;AAChB,SAAS,qBAAqB,CAAqB;IACjD,gBAAgB;IAChB,MAAM,uBAAuB;QAC3B;QACA;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,aAAa,qBAAsB;QAC5C,IAAI,EAAE,WAAW,MAAM,GAAG,GAAG;YAC3B,OAAO;QACT;IACF;IAEA,wBAAwB;IACxB,MAAM,cAAc,EAAE,IAAI;IAC1B,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AACnD;AAEA,oBAAoB;AACpB,SAAS,yBAAyB,CAAqB;IACrD,MAAM,kBAAkB,EAAE;IAE1B,oBAAoB;IACpB,MAAM,cAAc,EAAE;IACtB,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,aAAa,YAAY,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG;YACpD,MAAM,MAAM,EAAE;YACd,IAAI,OAAO,IAAI,IAAI,GAAG,IAAI;YAE1B,aAAa;YACb,IAAI,CAAC,QACD,KAAK,MAAM,GAAG,KACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QACpC,IAAI,QAAQ,CAAC,aACb,IAAI,QAAQ,CAAC,WAAW;gBAC1B,OAAO;YACT;YAEA,gBAAgB;YAChB,OAAO,KAAK,OAAO,CAAC,WAAW,MAAM,UAAU;YAC/C,OAAO,KAAK,OAAO,CAAC,QAAQ,MAAM,SAAS;YAC3C,OAAO,KAAK,IAAI;YAEhB,OAAO;QACT,GAAG,GAAG,GAAG,MAAM,CAAC;QAEhB,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,gBAAgB,IAAI,IAAI;QAC1B;IACF;IAEA,6BAA6B;IAC7B,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,MAAM,cAAc,EAAE;QACtB,YAAY,IAAI,CAAC,CAAC,GAAG;YACnB,MAAM,OAAO,EAAE,IAAI,IAAI,GAAG,IAAI;YAC9B,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI;gBAC5B,gBAAgB,IAAI,CAAC;YACvB;QACF;IACF;IAEA,oBAAoB;IACpB,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,OAAO,eAAe;IACxB;IAEA,OAAO,gBAAgB,IAAI,CAAC;AAC9B;AAEA,4BAA4B;AAC5B,SAAS,uBAAuB,CAAqB;IACnD,OAAO,EAAE,gBAAgB,MAAM,GAAG,KAAK,EAAE,YAAY,MAAM,GAAG;AAChE;AAEA,yBAAyB;AACzB,eAAe,yBAAyB,CAAqB;IAC3D,MAAM,SAA2B;QAC/B,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,aAAa;QACb,WAAW;QACX,SAAS;QACT,YAAY;QACZ,SAAS;QACT,QAAQ,EAAE;QACV,YAAY;QACZ,aAAa;IACf;IAEA,uBAAuB;IACvB,MAAM,cAAc,EAAE;IACtB,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,wBAAwB;QACxB,MAAM,eAAe,YAAY,IAAI,CAAC,MAAM,KAAK;QACjD,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,gBAAgB;YAChB,MAAM,aAAa,aAAa,KAAK;YAErC,eAAe;YACf,WAAW,IAAI,CAAC,OAAO,MAAM;YAE7B,eAAe;YACf,MAAM,YAAY,WAAW,QAAQ,GAAG,MAAM,CAAC;gBAC7C,OAAO,IAAI,CAAC,QAAQ,KAAK,GAAG,OAAO;YACrC,GAAG,IAAI,GAAG,IAAI;YAEd,iBAAiB;YACjB,MAAM,cAAc,WAAW,IAAI,CAAC,OAAO,KAAK;YAChD,IAAI,WAAW;YACf,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,WAAW,YAAY,IAAI,GAAG,IAAI;gBAClC,aAAa;gBACb,WAAW,SAAS,OAAO,CAAC,OAAO,IAAI,IAAI;YAC7C;YAEA,cAAc;YACd,IAAI,WAAW;gBACb,OAAO,KAAK,GAAG;gBACf,IAAI,UAAU;oBACZ,OAAO,QAAQ,GAAG;gBACpB;YACF,OAAO;gBACL,6BAA6B;gBAC7B,MAAM,YAAY,aAAa,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC,QAAQ;gBAC7D,cAAc;gBACd,IAAI,UAAU,QAAQ,CAAC,OAAO;oBAC5B,MAAM,QAAQ,UAAU,KAAK,CAAC;oBAC9B,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI;oBAC5B,OAAO,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI;gBACjC,OAAO;oBACL,OAAO,KAAK,GAAG;gBACjB;YACF;QACF;QAEA,2BAA2B;QAC3B,MAAM,aAAa,YAAY,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,GAAG;YACrD,OAAO,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC;QAC/B,GAAG,KAAK;QAER,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,cAAc;YACd,MAAM,aAAa,WAAW,IAAI,CAAC,KAAK,KAAK;YAC7C,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,OAAO,MAAM,GAAG,WAAW,IAAI,GAAG,IAAI;gBACtC,OAAO,SAAS,GAAG,WAAW,IAAI,CAAC,WAAW;YAChD,OAAO;gBACL,qBAAqB;gBACrB,MAAM,WAAW,WAAW,IAAI,GAAG,IAAI;gBACvC,OAAO,MAAM,GAAG,SAAS,OAAO,CAAC,eAAe,IAAI,IAAI;YAC1D;QACF;QAEA,uBAAuB;QACvB,IAAI,CAAC,OAAO,MAAM,EAAE;YAClB,YAAY,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG;gBAC7B,MAAM,OAAO,EAAE,IAAI,IAAI,GAAG,IAAI;gBAC9B,IAAI,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO;oBAC5F,oBAAoB;oBACpB,OAAO,MAAM,GAAG,KAAK,OAAO,CAAC,kCAAkC,IAAI,IAAI;oBACvE,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,IAAI,OAAO,MAAM,GAAG,IAAI,aAAa;oBAChE,OAAO,OAAO,QAAQ;gBACxB;YACF;QACF;IAEA,kCAAkC;IACpC;IAEA,mBAAmB;IACnB,MAAM,aAAa,EAAE;IACrB,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,IAAI,cAAc,WAAW,IAAI,GAAG,IAAI;QAExC,oBAAoB;QACpB,cAAc,YAAY,OAAO,CAAC,gBAAgB;QAElD,qBAAqB;QACrB,IAAI,eAAe,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG;YAChD,OAAO,OAAO,GAAG,YAAY,IAAI;YACjC,OAAO,UAAU,GAAG,MAAM,UAAU;QACtC;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,EAAE,4BAA4B,KAAK;IACvD,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,WAAW;QACX,MAAM,eAAe,YAAY,KAAK;QACtC,aAAa,IAAI,CAAC,2EAA2E,MAAM;QAEnG,OAAO;QACP,MAAM,aAAa,aAAa,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG;YACrD,MAAM,OAAO,EAAE,IAAI,IAAI,GAAG,IAAI;YAC9B,6BAA6B;YAC7B,IAAI,KAAK,MAAM,GAAG,MACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,SACd,KAAK,QAAQ,CAAC,WACd,KAAK,QAAQ,CAAC,UACd,KAAK,QAAQ,CAAC,sBACd,KAAK,QAAQ,CAAC,kBAAkB;gBAClC,OAAO;YACT;YACA,OAAO;QACT,GAAG,GAAG,GAAG,MAAM,CAAC;QAEhB,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,OAAO,OAAO,GAAG,WAAW,IAAI,CAAC;QACnC,OAAO;YACL,kBAAkB;YAClB,OAAO,OAAO,GAAG,aAAa,IAAI,GAAG,IAAI;QAC3C;QAEA,aAAa;QACb,YAAY,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG;YAC/B,MAAM,MAAM,EAAE,KAAK,IAAI,CAAC;YACxB,IAAI,KAAK;gBACP,gBAAgB;gBAChB,IAAI,UAAU;gBACd,IAAI,IAAI,UAAU,CAAC,OAAO;oBACxB,UAAU,WAAW;gBACvB,OAAO,IAAI,IAAI,UAAU,CAAC,MAAM;oBAC9B,qBAAqB;oBACrB;gBACF;gBAEA,IAAI,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,UAAU;oBACpC,OAAO,MAAM,CAAC,IAAI,CAAC;gBACrB;YACF;QACF;IACF;IAEA,cAAc;IACd,MAAM,cAAc,EAAE;IACtB,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,WAAW,YAAY,IAAI,GAAG,IAAI;QACxC,IAAI,SAAS,QAAQ,CAAC,OAAO;YAC3B,oBAAoB;YACpB,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,OAAO,OAAO,GAAG;YACnB;QACF;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,EAAE;IACrB,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,OAAO,UAAU,GAAG,WAAW,IAAI,GAAG,IAAI;IAC5C;IAEA,sBAAsB;IACtB,IAAI,CAAC,OAAO,KAAK,EAAE;QACjB,OAAO,KAAK,GAAG,aAAa;IAC9B;IAEA,sBAAsB;IACtB,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,OAAO,MAAM,GAAG,cAAc;IAChC;IAEA,iCAAiC;IAEjC,mBAAmB;IACnB,IAAI,OAAO,SAAS,EAAE;QACpB,IAAI;YACF,MAAM,iBAAiB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEA,SAAS;AACT,eAAe,iBAAiB,MAAwB;IACtD,IAAI,CAAC,OAAO,SAAS,EAAE;IAEvB,IAAI;QACF,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;QAEvD,MAAM,WAAW,MAAM,MAAM,OAAO,SAAS,EAAE;YAC7C,SAAS;gBACP,cAAc;gBACd,UAAU;YACZ;YACA,QAAQ,WAAW,MAAM;QAC3B;QAEA,aAAa;QAEb,IAAI,CAAC,SAAS,EAAE,EAAE;QAElB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,IAAI,yJAAA,CAAA,OAAY,CAAC;QAEvB,SAAS;QACT,MAAM,WAAW,EAAE,qBAAqB,KAAK;QAC7C,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,WAAW,GAAG,SAAS,IAAI,CAAC,UAAU;QAC/C;QAEA,SAAS;QACT,MAAM,SAAS,EAAE,kBAAkB,KAAK;QACxC,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO,SAAS,GAAG,OAAO,IAAI,GAAG,IAAI;QACvC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;IAC7B;AACF", "debugId": null}}]}
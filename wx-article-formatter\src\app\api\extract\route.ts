import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import * as cheerio from 'cheerio';

interface ExtractedContent {
  title: string;
  subtitle?: string;
  author: string;
  authorUrl?: string;
  authorPhoto?: string;
  authorBio?: string;
  summary: string;
  hasSummary?: boolean; // 标识是否真正有摘要
  content: string;
  images: string[];
  editorNote: string;
  publishDate?: string;
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();
    
    if (!url) {
      return NextResponse.json({ error: '请提供有效的URL' }, { status: 400 });
    }

    // 获取网页内容
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      timeout: 15000,
      maxRedirects: 5,
      responseType: 'text'
    });

    const $ = cheerio.load(response.data) as any;

    // 检测是否为附件HTML格式
    const isAttachmentHtml = detectAttachmentHtml($);

    // 检测是否为银河悦读等特定格式
    const isArticleBoxFormat = detectArticleBoxFormat($);

    // 提取内容的策略选择
    let extractedContent: ExtractedContent;

    if (isArticleBoxFormat) {
      extractedContent = await extractArticleBoxContent($);
    } else if (isAttachmentHtml) {
      extractedContent = {
        title: extractTitle($),
        subtitle: '',
        author: extractAuthor($),
        authorUrl: '',
        authorPhoto: '',
        authorBio: '',
        summary: extractSummary($),
        hasSummary: false, // 其他格式不显示摘要
        content: extractAttachmentContent($),
        images: extractImages($),
        editorNote: extractEditorNote($),
        publishDate: extractPublishDate($)
      };
    } else {
      extractedContent = {
        title: extractTitle($),
        subtitle: '',
        author: extractAuthor($),
        authorUrl: '',
        authorPhoto: '',
        authorBio: '',
        summary: extractSummary($),
        hasSummary: false, // 其他格式不显示摘要
        content: extractContent($),
        images: extractImages($),
        editorNote: extractEditorNote($),
        publishDate: extractPublishDate($)
      };
    }

    return NextResponse.json(extractedContent);
  } catch (error) {
    console.error('提取内容失败:', error);
    return NextResponse.json(
      { error: '提取内容失败，请检查URL是否有效' }, 
      { status: 500 }
    );
  }
}

// 提取标题
function extractTitle($: cheerio.CheerioAPI): string {
  // 尝试多种选择器，包括常见的CMS和附件格式
  const titleSelectors = [
    'h1',
    '.title',
    '.article-title',
    '.post-title',
    '.entry-title',
    '.content-title',
    '.main-title',
    '.doc-title',
    '.attachment-title',
    '[class*="title"]',
    '[id*="title"]',
    'title',
    // 针对附件HTML格式
    '.WordSection1 h1',
    '.WordSection1 p[style*="font-size:18"]',
    '.WordSection1 p[style*="font-size:20"]',
    '.WordSection1 p[style*="font-size:22"]',
    '.WordSection1 p[style*="font-weight:bold"]',
    // 微信公众号格式
    '#activity-name',
    '.rich_media_title',
    // 知乎、简书等格式
    '.Post-Title',
    '.article h1',
    '.note h1'
  ];

  for (const selector of titleSelectors) {
    const title = $(selector).first().text().trim();
    if (title && title.length > 0 && title.length < 200) {
      return title;
    }
  }

  // 如果没有找到标题，尝试从第一个较大的文本块提取
  const firstLargeText = $('p, div').filter((_, el) => {
    const text = $(el).text().trim();
    return text.length > 10 && text.length < 100;
  }).first().text().trim();

  return firstLargeText || '未找到标题';
}

// 提取作者
function extractAuthor($: cheerio.CheerioAPI): string {
  const authorSelectors = [
    '.author',
    '.byline',
    '.writer',
    '.post-author',
    '.article-author',
    '.content-author',
    '[class*="author"]',
    '[class*="byline"]',
    '[class*="writer"]',
    // 微信公众号格式
    '.rich_media_meta_text',
    '#profileBt',
    // 知乎、简书等格式
    '.UserLink',
    '.author-name',
    // 附件HTML格式中可能的作者信息
    '.WordSection1 p:contains("作者")',
    '.WordSection1 p:contains("撰写")',
    'meta[name="author"]'
  ];

  for (const selector of authorSelectors) {
    let author = '';
    if (selector.startsWith('meta')) {
      author = $(selector).attr('content') || '';
    } else {
      author = $(selector).first().text().trim();
      // 清理作者信息
      author = author.replace(/^(作者[:：]?|撰写[:：]?|by[:：]?\s*)/i, '').trim();
    }

    if (author && author.length > 0 && author.length < 50) {
      return author;
    }
  }

  return '未知作者';
}

// 提取摘要
function extractSummary($: cheerio.CheerioAPI): string {
  const summarySelectors = [
    '.summary',
    '.abstract',
    '.excerpt',
    '.description',
    '[class*="summary"]',
    '[class*="abstract"]',
    'meta[name="description"]'
  ];

  for (const selector of summarySelectors) {
    let summary = '';
    if (selector.startsWith('meta')) {
      summary = $(selector).attr('content') || '';
    } else {
      summary = $(selector).first().text().trim();
    }

    // 清理摘要文本，去掉重复的"摘要："
    summary = summary.replace(/^摘要[：:]?\s*/i, '');

    if (summary && summary.trim().length > 5) {
      return summary.trim();
    }
  }

  // 如果没有找到摘要，尝试从正文前几段提取
  const firstParagraphs = $('p').slice(0, 3).map((_, el) => $(el).text().trim()).get();
  let summary = firstParagraphs.join(' ').substring(0, 200);

  // 清理提取的摘要
  summary = summary.replace(/^摘要[：:]?\s*/i, '');

  return summary.trim() || '';
}

// 提取正文内容
function extractContent($: cheerio.CheerioAPI): string {
  const contentSelectors = [
    '.content',
    '.article-content',
    '.post-content',
    '.entry-content',
    '.main-content',
    '.text-content',
    '.rich_media_content',
    '[class*="content"]',
    'article',
    // 针对附件HTML格式
    '.WordSection1',
    '.MsoNormal',
    '.document-content',
    // 微信公众号格式
    '#js_content',
    '.rich_media_area_primary',
    // 知乎、简书等格式
    '.Post-RichText',
    '.note .content',
    'main'
  ];

  for (const selector of contentSelectors) {
    const content = $(selector).first();
    if (content.length > 0) {
      // 移除不需要的元素
      content.find('script, style, nav, header, footer, aside, .ad, .advertisement, .share, .comment').remove();

      // 获取文本内容，保留段落结构
      const paragraphs = content.find('p, div').map((_, el) => {
        const $el = $(el);
        const text = $el.text().trim();

        // 过滤掉太短或明显是导航/广告的内容
        if (text.length < 10 ||
            text.includes('点击') ||
            text.includes('关注') ||
            text.includes('分享') ||
            text.includes('广告') ||
            $el.hasClass('ad') ||
            $el.hasClass('share')) {
          return null;
        }

        return text;
      }).get().filter(Boolean);

      if (paragraphs.length > 0) {
        return paragraphs.join('\n\n');
      }

      // 如果没有段落，直接获取文本内容
      const textContent = content.text().trim();
      if (textContent.length > 50) {
        return textContent;
      }
    }
  }

  // 如果没有找到特定的内容区域，尝试提取所有段落
  const allParagraphs = $('p, div').map((_, el) => {
    const text = $(el).text().trim();
    // 过滤掉太短的段落和明显的导航内容
    if (text.length < 20 ||
        text.includes('首页') ||
        text.includes('导航') ||
        text.includes('菜单') ||
        text.includes('版权') ||
        text.includes('Copyright')) {
      return null;
    }
    return text;
  }).get().filter(Boolean);

  return allParagraphs.length > 0 ? allParagraphs.join('\n\n') : '未找到正文内容';
}

// 提取图片
function extractImages($: cheerio.CheerioAPI): string[] {
  const images: string[] = [];
  
  // 查找文章内容区域的图片
  const contentSelectors = [
    '.content img',
    '.article-content img',
    '.post-content img',
    '.entry-content img',
    'article img'
  ];

  for (const selector of contentSelectors) {
    $(selector).each((_, el) => {
      const src = $(el).attr('src');
      if (src && !images.includes(src)) {
        // 处理相对路径
        if (src.startsWith('//')) {
          images.push('https:' + src);
        } else if (src.startsWith('/')) {
          // 需要基础URL，这里暂时跳过
        } else if (src.startsWith('http')) {
          images.push(src);
        }
      }
    });
  }

  return images.slice(0, 10); // 限制最多10张图片
}

// 提取编者按
function extractEditorNote($: cheerio.CheerioAPI): string {
  const editorNoteSelectors = [
    '.editor-note',
    '.editorial',
    '.note',
    '[class*="editor"]',
    '[class*="note"]'
  ];

  for (const selector of editorNoteSelectors) {
    const note = $(selector).first().text().trim();
    if (note && note.length > 0) {
      return note;
    }
  }

  return '';
}

// 提取发布日期
function extractPublishDate($: cheerio.CheerioAPI): string {
  const dateSelectors = [
    '.publish-date',
    '.date',
    '.post-date',
    '[class*="date"]',
    'time',
    'meta[property="article:published_time"]'
  ];

  for (const selector of dateSelectors) {
    let date = '';
    if (selector.startsWith('meta')) {
      date = $(selector).attr('content') || '';
    } else if (selector === 'time') {
      date = $(selector).attr('datetime') || $(selector).text().trim();
    } else {
      date = $(selector).first().text().trim();
    }
    
    if (date && date.length > 0) {
      return date;
    }
  }

  return '';
}

// 检测是否为附件HTML格式
function detectAttachmentHtml($: cheerio.CheerioAPI): boolean {
  // 检查常见的附件HTML特征
  const attachmentIndicators = [
    '.WordSection1',
    '.MsoNormal',
    '[class*="Mso"]',
    'style[type="text/css"]',
    'meta[name="Generator"][content*="Microsoft"]',
    'meta[name="ProgId"][content*="Word"]'
  ];

  for (const indicator of attachmentIndicators) {
    if ($(indicator).length > 0) {
      return true;
    }
  }

  // 检查HTML内容是否包含Word特有的样式
  const htmlContent = $.html();
  const wordPatterns = [
    /mso-/i,
    /WordSection/i,
    /MsoNormal/i,
    /Microsoft Office Word/i,
    /<!--\[if gte mso/i
  ];

  return wordPatterns.some(pattern => pattern.test(htmlContent));
}

// 专门处理附件HTML格式的内容提取
function extractAttachmentContent($: cheerio.CheerioAPI): string {
  const contentSections = [];

  // 优先从WordSection1提取
  const wordSection = $('.WordSection1');
  if (wordSection.length > 0) {
    const paragraphs = wordSection.find('p, div').map((_, el) => {
      const $el = $(el);
      let text = $el.text().trim();

      // 跳过空段落和页眉页脚
      if (!text ||
          text.length < 5 ||
          text.includes('页码') ||
          text.includes('第') && text.includes('页') ||
          $el.hasClass('header') ||
          $el.hasClass('footer')) {
        return null;
      }

      // 清理Word特有的格式字符
      text = text.replace(/\u00A0/g, ' '); // 替换不间断空格
      text = text.replace(/\s+/g, ' '); // 合并多个空格
      text = text.trim();

      return text;
    }).get().filter(Boolean);

    if (paragraphs.length > 0) {
      contentSections.push(...paragraphs);
    }
  }

  // 如果WordSection1没有内容，尝试其他选择器
  if (contentSections.length === 0) {
    const msoElements = $('.MsoNormal, [class*="Mso"]');
    msoElements.each((_, el) => {
      const text = $(el).text().trim();
      if (text && text.length > 10) {
        contentSections.push(text);
      }
    });
  }

  // 如果还是没有内容，使用通用提取方法
  if (contentSections.length === 0) {
    return extractContent($);
  }

  return contentSections.join('\n\n');
}

// 检测是否为article-box格式（银河悦读等）
function detectArticleBoxFormat($: cheerio.CheerioAPI): boolean {
  return $('.article-box').length > 0 || $('#content').length > 0;
}

// 专门处理article-box格式的内容提取
async function extractArticleBoxContent($: cheerio.CheerioAPI): Promise<ExtractedContent> {
  const result: ExtractedContent = {
    title: '',
    subtitle: '',
    author: '',
    authorUrl: '',
    authorPhoto: '',
    authorBio: '',
    summary: '',
    hasSummary: false,
    content: '',
    images: [],
    editorNote: '',
    publishDate: ''
  };

  // 从article-head提取标题和作者
  const articleHead = $('.article-head');
  if (articleHead.length > 0) {
    // 提取标题 - 优化处理h1标签内的复杂结构
    const titleElement = articleHead.find('h1').first();
    if (titleElement.length > 0) {
      // 克隆元素以避免修改原DOM
      const titleClone = titleElement.clone();

      // 移除图片标签，只保留文本
      titleClone.find('img').remove();

      // 提取主标题（第一行文本）
      const titleText = titleClone.contents().filter(function() {
        return this.nodeType === 3; // 文本节点
      }).text().trim();

      // 提取副标题（div中的内容）
      const subtitleDiv = titleClone.find('div').first();
      let subtitle = '';
      if (subtitleDiv.length > 0) {
        subtitle = subtitleDiv.text().trim();
        // 去掉副标题前的破折号
        subtitle = subtitle.replace(/^——/, '').trim();
      }

      // 分别设置主标题和副标题
      if (titleText) {
        result.title = titleText;
        if (subtitle) {
          result.subtitle = subtitle;
        }
      } else {
        // 如果没有提取到文本节点，使用整个h1的文本但清理格式
        const fullTitle = titleElement.text().trim().replace(/\s+/g, ' ');
        // 尝试分离主标题和副标题
        if (fullTitle.includes('——')) {
          const parts = fullTitle.split('——');
          result.title = parts[0].trim();
          result.subtitle = parts[1].trim();
        } else {
          result.title = fullTitle;
        }
      }
    }

    // 提取作者 - 专门处理嵌套的span和a标签结构
    const authorSpan = articleHead.find('span').filter((_, el) => {
      return $(el).text().includes('作者：');
    }).first();

    if (authorSpan.length > 0) {
      // 查找span内的a标签
      const authorLink = authorSpan.find('a').first();
      if (authorLink.length > 0) {
        result.author = authorLink.text().trim();
        result.authorUrl = authorLink.attr('href') || '';
      } else {
        // 如果没有a标签，从span文本中提取
        const spanText = authorSpan.text().trim();
        result.author = spanText.replace(/^作者[:：]?\s*/, '').trim();
      }
    }

    // 如果上面的方法没有找到作者，使用通用方法
    if (!result.author) {
      articleHead.find('*').each((_, el) => {
        const text = $(el).text().trim();
        if (text.includes('作者') || text.includes('撰写') || text.includes('by') || text.includes('By')) {
          // 提取作者名字，去掉"作者："等前缀
          result.author = text.replace(/^(作者[:：]?|撰写[:：]?|by[:：]?\s*)/i, '').trim();
          if (result.author.length > 50) result.author = ''; // 太长的可能不是作者名
          return false; // 找到后停止
        }
      });
    }

    // 不从头部提取图片，因为头部的图片通常是装饰性的（如推荐图标等）
  }

  // 从article-abs提取摘要
  const articleAbs = $('.article-abs');
  if (articleAbs.length > 0) {
    let summaryText = articleAbs.text().trim();

    // 清理摘要文本，去掉重复的"摘要："
    summaryText = summaryText.replace(/^摘要[：:]?\s*/i, '');

    // 只有当清理后的摘要有实际内容时才使用
    if (summaryText && summaryText.trim().length > 5) {
      result.summary = summaryText.trim();
      result.hasSummary = true; // 标记真正有摘要
    }
  }

  // 从article-text或#content1提取正文
  const articleText = $('.article-text, #content1').first();
  if (articleText.length > 0) {
    // 移除不需要的元素
    const contentClone = articleText.clone();
    contentClone.find('script, style, .ad, .advertisement, .share, .comment, .nav, .navigation').remove();

    // 提取段落
    const paragraphs = contentClone.find('p, div').map((_, el) => {
      const text = $(el).text().trim();
      // 过滤掉太短的段落和明显的导航/广告内容
      if (text.length < 10 ||
          text.includes('点击') ||
          text.includes('关注') ||
          text.includes('分享') ||
          text.includes('广告') ||
          text.includes('评论') ||
          text.includes('发表时间') ||
          text.includes('阅读量')) {
        return null;
      }
      return text;
    }).get().filter(Boolean);

    if (paragraphs.length > 0) {
      result.content = paragraphs.join('\n\n');
    } else {
      // 如果没有段落，直接获取文本内容
      result.content = contentClone.text().trim();
    }

    // 只从正文区域提取图片
    articleText.find('img').each((_, img) => {
      const src = $(img).attr('src');
      if (src) {
        // 处理相对路径和协议相对路径
        let fullSrc = src;
        if (src.startsWith('//')) {
          fullSrc = 'https:' + src;
        } else if (src.startsWith('/')) {
          // 需要基础URL，这里暂时跳过相对路径
          return;
        }

        if (!result.images.includes(fullSrc)) {
          result.images.push(fullSrc);
        }
      }
    });
  }

  // 提取标签（如果有的话）
  const tagsElement = $('.article-notes-title, p.article-notes-title');
  if (tagsElement.length > 0) {
    const tagsText = tagsElement.text().trim();
    if (tagsText.includes('标签')) {
      // 可以将标签信息添加到摘要或其他字段
      if (!result.summary) {
        result.summary = tagsText;
      }
    }
  }

  // 提取编者按
  const editorNote = $('.article-notes-body, p.article-notes-body');
  if (editorNote.length > 0) {
    result.editorNote = editorNote.text().trim();
  }

  // 如果没有提取到标题，尝试从其他地方获取
  if (!result.title) {
    result.title = extractTitle($);
  }

  // 如果没有提取到作者，尝试从其他地方获取
  if (!result.author) {
    result.author = extractAuthor($);
  }

  // 不自动生成摘要，只有真正存在article-abs时才有摘要

  // 如果有作者链接，尝试提取作者简介
  if (result.authorUrl) {
    try {
      await extractAuthorBio(result);
    } catch (error) {
      console.error('提取作者简介失败:', error);
      // 不影响主要功能，继续执行
    }
  }

  return result;
}

// 提取作者简介
async function extractAuthorBio(result: ExtractedContent) {
  if (!result.authorUrl) return;

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(result.authorUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) return;

    const html = await response.text();
    const $ = cheerio.load(html);

    // 提取作者照片
    const photoDiv = $('.zj-block-pic img').first();
    if (photoDiv.length > 0) {
      result.authorPhoto = photoDiv.attr('src') || '';
    }

    // 提取作者简介
    const bioDiv = $('.zj-block-cont').first();
    if (bioDiv.length > 0) {
      result.authorBio = bioDiv.text().trim();
    }
  } catch (error) {
    console.error('获取作者页面失败:', error);
  }
}
